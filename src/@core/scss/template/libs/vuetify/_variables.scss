$font-family-custom: "Inter", sans-serif, -apple-system, blinkmacsystemfont, "Segoe UI", roboto,
  "Helvetica Neue", arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
/* stylelint-disable length-zero-no-unit */
@forward "../../../base/libs/vuetify/variables" with (
  $body-font-family: $font-family-custom !default,
  $border-radius-root: 6px,

  $rounded: (
    "xs": 2px,
    "sm": 4px,
    "shaped": 25px 0,
    "lg":8px,
    "xl": 10px,
  ) !default,

  // 👉 Typography
  $typography: (
    "h1": (
      "size": 2.875rem,
      "weight": 500,
      "line-height": 4.25rem,
      "letter-spacing": normal,
    ),
    "h2": (
      "size": 2.375rem,
      "weight": 500,
      "line-height": 3.5rem,
      "letter-spacing": normal,
    ),
    "h3": (
      "size": 1.75rem,
      "weight": 500,
      "line-height": 2.625rem,
      "letter-spacing": normal,
    ),
    "h4": (
      "size": 1.5rem,
      "weight": 500,
      "line-height": 2.375rem,
      "letter-spacing": normal,
    ),
    "h5": (
      "size": 1.125rem,
      "weight": 500,
      "line-height": 1.75rem,
      "letter-spacing": normal,
    ),
    "h6": (
      "size": 0.9375rem,
      "weight": 500,
      "line-height": 1.375rem,
      "letter-spacing": normal,
    ),
    "subtitle-1": (
      "size": 0.9375rem,
      "line-height": 1.375rem,
      "letter-spacing": normal,
    ),
    "subtitle-2": (
      "size": 0.8125rem,
      "line-height": 1.25rem,
      "letter-spacing": normal,
    ),
    "body-1": (
      "size": 0.9375rem,
      "line-height": 1.375rem,
      "letter-spacing": normal,
    ),
    "body-2": (
      "size": 0.8125rem,
      "line-height": 1.25rem,
      "letter-spacing": normal,
    ),
    "caption": (
      "size": 0.8125rem,
      "line-height": 1.125rem,
      "letter-spacing": 0.025rem,
    ),
    "overline": (
      "size": 0.75rem,
      "weight": 400,
      "line-height": 0.875rem,
      "letter-spacing": 0.05rem,
      "text-transform": uppercase,
    ),
    "button": (
      "size": 0.9375rem,
      "weight": 500,
      "letter-spacing": normal,
      "font-family": $font-family-custom,
      "text-transform": capitalize,
    ),
  )!default,

  //  👉 Shadows
  $shadow-key-umbra: (
    0: (0 0 0 0 rgba(var(--v-shadow-key-umbra-color), 1)),
    1: (0 2px 4px rgba(var(--v-shadow-key-umbra-color), 0.12)),
    2: (0 2px 6px 0 rgba(var(--v-shadow-key-umbra-color), var(--v-shadow-xs-opacity))),
    3: (0 3px 8px rgba(var(--v-shadow-key-umbra-color), 0.14)),
    4: (0 2px 10px 0 rgba(var(--v-shadow-key-umbra-color), var(--v-shadow-sm-opacity))),
    5: (0 4px 10px rgba(var(--v-shadow-key-umbra-color), 0.15)),
    6: (0 4px 14px 0 rgba(var(--v-shadow-key-umbra-color), var(--v-shadow-md-opacity))),
    7: (0 4px 18px rgba(var(--v-shadow-key-umbra-color), 0.1)),
    8: (0 6px 20px 0 rgba(var(--v-shadow-key-umbra-color), var(--v-shadow-lg-opacity))),
    9: (0 5px 14px rgba(var(--v-shadow-key-umbra-color), 0.18)),
    10: (0 8px 26px 0 rgba(var(--v-shadow-key-umbra-color), var(--v-shadow-xl-opacity))),
    11: (0 5px 16px rgba(var(--v-shadow-key-umbra-color), 0.2)),
    12: (0 6px 17px rgba(var(--v-shadow-key-umbra-color), 0.22)),
    13: (0 6px 18px rgba(var(--v-shadow-key-umbra-color), 0.22)),
    14: (0 6px 19px rgba(var(--v-shadow-key-umbra-color), 0.24)),
    15: (0 7px 20px rgba(var(--v-shadow-key-umbra-color), 0.24)),
    16: (0 7px 21px rgba(var(--v-shadow-key-umbra-color), 0.26)),
    17: (0 7px 22px rgba(var(--v-shadow-key-umbra-color), 0.26)),
    18: (0 8px 23px rgba(var(--v-shadow-key-umbra-color), 0.28)),
    19: (0 8px 24px 6px rgba(var(--v-shadow-key-umbra-color), 0.28)),
    20: (0 9px 25px rgba(var(--v-shadow-key-umbra-color), 0.3)),
    21: (0 9px 26px rgba(var(--v-shadow-key-umbra-color), 0.32)),
    22: (0 9px 27px rgba(var(--v-shadow-key-umbra-color), 0.32)),
    23: (0 10px 28px rgba(var(--v-shadow-key-umbra-color), 0.34)),
    24: (0 10px 30px rgba(var(--v-shadow-key-umbra-color), 0.34))
  ) !default,

  $shadow-key-penumbra: (
    0: (0 0 transparent),
    1: (0 0 transparent),
    2: (0 0 transparent),
    3: (0 0 transparent),
    4: (0 0 transparent),
    5: (0 0 transparent),
    6: (0 0 transparent),
    7: (0 0 transparent),
    8: (0 0 transparent),
    9: (0 0 transparent),
    10: (0 0 transparent),
    11: (0 0 transparent),
    12: (0 0 transparent),
    13: (0 0 transparent),
    14: (0 0 transparent),
    15: (0 0 transparent),
    16: (0 0 transparent),
    17: (0 0 transparent),
    18: (0 0 transparent),
    19: (0 0 transparent),
    20: (0 0 transparent),
    21: (0 0 transparent),
    22: (0 0 transparent),
    23: (0 0 transparent),
    24: (0 0 transparent),
  ) !default,

  $shadow-key-ambient: (
    0: (0 0 transparent),
    1: (0 0 transparent),
    2: (0 0 transparent),
    3: (0 0 transparent),
    4: (0 0 transparent),
    5: (0 0 transparent),
    6: (0 0 transparent),
    7: (0 0 transparent),
    8: (0 0 transparent),
    9: (0 0 transparent),
    10: (0 0 transparent),
    11: (0 0 transparent),
    12: (0 0 transparent),
    13: (0 0 transparent),
    14: (0 0 transparent),
    15: (0 0 transparent),
    16: (0 0 transparent),
    17: (0 0 transparent),
    18: (0 0 transparent),
    19: (0 0 transparent),
    20: (0 0 transparent),
    21: (0 0 transparent),
    22: (0 0 transparent),
    23: (0 0 transparent),
    24: (0 0 transparent),
  ) !default,

  // 👉 Avatar
  $avatar-color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity)) !default,

  // 👉 Alert
  $alert-title-font-size: 1.125rem !default,
  $alert-border-radius: 8px !default,

  // 👉 Autocomplete
  $autocomplete-content-elevation: 8 !default,
  $combobox-content-elevation: 8 !default,
  $select-content-elevation: 8 !default,

  // 👉 Badge
  $badge-height: 1.375rem !default,
  $badge-min-width: 1.375rem !default,
  $badge-dot-height: 8px !default,
  $badge-dot-width: 8px !default,
  $badge-dot-border-width: 2px !default,
  $badge-border-radius: 50px !default,
  $badge-font-size: 0.8125rem !default,
  $badge-border-color: rgb(var(--v-theme-surface)) !default,
  $badge-border-transform: scale(1.5) !default,

  // 👉 Buttons
  $button-height: 38px !default,
  $button-padding-ratio: 1.673 !default,
  $button-margin-start: 0 !default,
  $button-disabled-opacity: 0.45 !default,
  $button-elevation: ("default": 2, "hover": 2, "active": 0) !default,
  $button-density: ("default": 0, "comfortable": -1, "compact": -2) !default,
  $button-line-height: 1.375rem !default,
  $button-margin-end: 0.5rem !default,
  $button-border-radius: 0.5rem !default,
  $button-card-actions-padding: 0 18px !default,
  $button-pagination-border-radius: 8px !default,

  // 👉 Carousel
  $carousel-dot-inactive-opacity: 1 !default,
  $carousel-dot-margin: 0 !default,
  $carousel-controls-color: rgba(var(--v-theme-on-surface), var(--v-selected-opacity)) !default,

  // 👉 Chip
  $chip-font-size: 13px !default,
  $chip-close-size: 20px !default,
  $chip-font-weight: 500 !default,
  $chip-height: 32px !default,
  $chip-elevation: 0 !default,

  // 👉 Cards
  $card-actions-padding: 0 8px 8px !default,
  $card-title-font-size: 1.125rem !default,
  $card-title-line-height: 1.75rem !default,
  $card-text-font-size: 0.9375rem !default,
  $card-text-line-height: 1.375rem !default,
  $card-subtitle-font-size: 0.9375rem !default,
  $card-subtitle-line-height: 1.375rem !default,
  $card-subtitle-font-weight: 400 !default,
  $card-subtitle-header-padding: 0 !default,
  $card-border-radius: 10px !default,

  // 👉 Dialog
  $dialog-elevation: 8 !default,
  $dialog-border-radius: 10px !default,
  $dialog-card-header-padding: 20px 20px 0 !default,
  $dialog-card-header-text-padding-top: 20px !default,
  $dialog-card-text-padding: 20px !default,

  // 👉 Expansion Panel
  $expansion-panel-active-margin: 0.5rem !default,
  $expansion-panel-border-radius: 10px !default,
  $expansion-panel-text-padding: 0 20px 20px !default,
  $expansion-panel-title-padding: 12px 20px !default,
  $expansion-panel-title-min-height: 46px !default,
  $expansion-panel-active-title-min-height: 46px !default,

  // 👉 Field
  $field-outline-opacity: 0.22 !default,
  $field-control-affixed-padding: 16px !default,
  $field-control-affixed-inner-padding: 10px !default,
  $field-overlay-filled-opacity: var(--v-hover-opacity) !default,
  $field-border-radius: 0.5rem !default,
  $field-font-size: 15px !default,
  $input-details-padding-above: 0.25rem !default,
  $input-details-font-size: 0.8125rem !default,

  // 👉 Label
  $label-font-size: 0.9375rem !default,
  $label-letter-spacing: normal !default,

  // 👉 List
  $list-item-one-line-min-height: 38px !default,
  $list-item-padding: 8px 20px !default,
  $list-item-min-height: 38px !default,
  $list-subheader-font-size: 13px !default,
  $list-subheader-line-height: 1.25rem !default,
  $list-subheader-padding-end: 20px !default,
  $list-subheader-min-height: 40px !default,
  $list-disabled-opacity: 0.4 !default,
  $list-item-icon-margin-start: 12px !default,
  $list-item-icon-margin-end: 12px !default,
  $list-item-avatar-margin-start: 12px !default,
  $list-item-avatar-margin-end: 12px !default,
  $list-item-nav-title-font-size: 0.9375rem !default,
  $list-item-nav-title-font-weight: 400 !default,
  $list-item-nav-subtitle-font-size: 0.8125rem !default,
  $list-item-subtitle-line-height: 1.25rem !default,

  // 👉 label
  $field-label-floating-scale: 0.8125 !default,

  // 👉 Menu
  $menu-content-border-radius: 10px !default,

  // 👉 Snackbar
  $snackbar-background: rgb(var(--v-tooltip-background)) !default,
  $snackbar-color: rgb(var(--v-theme-surface)) !default,
  $snackbar-content-padding: 12px 16px !default,
  $snackbar-wrapper-padding: 0 !default,
  $snackbar-wrapper-min-height: 44px !default,
  $snackbar-elevation: 2 !default,
  $snackbar-action-margin: 16px !default,
  $snackbar-border-radius: 8px !default,

  // 👉 Slider
  $slider-thumb-hover-opacity: var(--v-activated-opacity) !default,

  // 👉 Tooltip
  $tooltip-background-color: rgb(var(--v-tooltip-background)) !default,
  $tooltip-text-color: rgb(var(--v-theme-surface)) !default,
  $tooltip-font-size: 0.8125rem !default,
  $tooltip-padding: 4px 12px !default,
  $tooltip-line-height: 1.25rem !default,

  // 👉 VPagination
  $pagination-item-margin: 0.1875rem !default,

  // 👉 Progress Linear
  $progress-linear-background-opacity: var(--v-activated-opacity) !default,

  // 👉 Tabs
  $tabs-height: 38px !default,
  $tab-min-width: 60px !default,

  // 👉 Timeline
  $timeline-divider-line-background: rgba(var(--v-border-color), var(--v-border-opacity)) !default,
  $timeline-divider-line-thickness: 2px !default,
  $timeline-item-padding: 16px !default,

  // 👉 Slider
  $slider-track-active-size-offset: 0px !default,
  $slider-thumb-label-border-radius: 6px !default,
  $slider-thumb-label-height: 28px !default,
  $slider-thumb-label-padding: 4px 10px !default,

  // 👉 Switch
  $switch-inset-track-width: 1.875rem !default,
  $switch-inset-track-height: 1.125rem !default,

  // 👉 Table
  $table-row-height: 50px !default,
  $table-header-height: 64px !default,
  $table-header-font-weight: 500 !default,
  $table-color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity)) !default,
  $table-font-size: 15px !default,
  $table-column-padding: 0 20px !default,
  $data-table-footer-padding: 16px 20px !default,

  // 👉 Radio
  $radio-group-label-selection-group-padding-inline: 0 !default,

  // 👉 navigation drawer
  $navigation-drawer-temporary-elevation: 8 !default,
  $navigation-drawer-transition-duration: 0.4s !default,

  // 👉 Messages
  $messages-font-size: 13px !default,
);
