import axios from 'axios'

const accessToken = JSON.parse(localStorage.getItem('accessToken'))

const axiosIns = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL,
  headers: {
    'Access-Control-Allow-Origin': ['*'],
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS, DELETE, PUT',
    'Access-Control-Allow-Headers': 'Origin, Content-Type, Accept, Authorization',
    Authorization: `Bearer ${accessToken}`,
  },
})

axiosIns.interceptors.request.use(function (config) {
  let token = JSON.parse(localStorage.getItem('accessToken'))
  config.headers['Authorization'] = 'Bearer ' + token

  return config
})

export default axiosIns
