import { defineStore } from 'pinia'
import axiosIns from '@/composables/axios'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'

export const useAdminStore = defineStore('Admin', () => {
  const users = ref(null)

  function $reset() {
    users.value = null
  }

  const getUsers = async data => {
    try {
      const res = await axiosIns.get('api/users', data)

      console.log({ res })

      const apiUsers =  res.data

      users.value = apiUsers
    } catch (error) {
      toast(error.response?.data?.message || 'Users Could Not Be Loaded', {
        autoClose: 5000,
        theme: 'light',
        type: 'error',
      })
    }
  }

  const createUser = async data => {
    try {
      const res = await axiosIns.post('api/users', data)

      console.log({ res })
    } catch (error) {
      toast(error.response?.data?.message || 'User Could Not Be Created!', {
        autoClose: 5000,
        theme: 'light',
        type: 'error',
      })
    }
  }

  const updateUser = async data => {
    try {
      const res = await axiosIns.put('api/users', data)

      console.log({ res })
    } catch (error) {
      toast(error.response?.data?.message || 'User Could Not Be Updated!', {
        autoClose: 5000,
        theme: 'light',
        type: 'error',
      })
    }
  }

  const deleteUser = async data => {
    try {
      const res = await axiosIns.delete('api/users', data)

      console.log({ res })
    } catch (error) {
      toast(error.response?.data?.message || 'User Could Not Be Deleted!', {
        autoClose: 5000,
        theme: 'light',
        type: 'error',
      })
    }
  }

  return {
    users,
    $reset,
    getUsers,
    createUser,
    updateUser,
    deleteUser,
  }
}, {
  persistedState: {
    persist: true,
  },
})
