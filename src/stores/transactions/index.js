import { defineStore } from 'pinia'
import axiosIns from '@/composables/axios'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'

export const useTransactionStore = defineStore('Transaction', () => {
  const transactions = ref(null)
  const transaction = ref(null)

  function $reset() {
    transactions.value = null
    transaction.value = null
  }

  const getTransactions = async data => {
    try {
      const res = await axiosIns.get('api/transactions', data)

      console.log({ res })

      const apiTransactions =  res.data

      transactions.value = apiTransactions
    } catch (error) {
      toast(error.response?.data?.message || 'Transactions Could Not Be Loaded', {
        autoClose: 5000,
        theme: 'light',
        type: 'error',
      })
    }
  }

  const getTransaction = async id => {
    try {
      const res = await axiosIns.get(`api/transactions/${id}`)

      console.log({ res })

      const apiTransaction =  res.data

      transaction.value = apiTransaction
    } catch (error) {
      toast(error.response?.data?.message || 'Transaction Does Not Exist', {
        autoClose: 5000,
        theme: 'light',
        type: 'error',
      })
    }
  }

  return {
    transactions,
    transaction,
    $reset,
    getTransactions,
    getTransaction,
  }
}, {
  persistedState: {
    persist: true,
  },
})
