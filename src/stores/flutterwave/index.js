import { defineStore } from 'pinia'
import axiosIns from '@/composables/axios'

export const useFlutterwaveStore = defineStore('Flutterwave', () => {
  const respData = ref('')

  function $reset() {
    respData.value = ''
  }

  const chargeApi = async data => {
    const res = await axiosIns.post('pay/mobilemoney', data)

    console.log(res.data.data)

    respData.value = res.data.data.link
  }

  return {
    respData,
    $reset,
    chargeApi,
  }
})
