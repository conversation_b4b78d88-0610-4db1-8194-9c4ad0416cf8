import { defineStore } from 'pinia'
import axiosIns from '@/composables/axios'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'

export const useAuthStore = defineStore('Auth', () => {
  const router = useRouter()

  const userAbilityRules = useCookie('userAbilityRules', { default: () => [] })
    
  const token = ref(null)
  const user = ref(null)

  function $reset() {
    token.value = null
    user.value = null
  }

  const login = async data => {
    try {
      const res = await axiosIns.post('login', data)

      const apiToken = res.data.token
      const apiUser =  res.data.user

      token.value = apiToken
      user.value = apiUser

      if (user.value.Role === 'admin') {
        userAbilityRules.value = [{
          action: 'manage',
          subject: 'all',
        }]

        console.log(userAbilityRules.value)

        localStorage.setItem('accessToken', JSON.stringify(token.value))
        localStorage.setItem('userData', JSON.stringify(user.value))
        localStorage.setItem('userRole', JSON.stringify({ role: user.value.Role }))
        localStorage.setItem('userAbilities', JSON.stringify([{
          action: 'manage',
          subject: 'all',
        }]))

        sessionStorage.setItem('accessToken', JSON.stringify(token.value))
        sessionStorage.setItem('userData', JSON.stringify(user.value))
        sessionStorage.setItem('userRole', JSON.stringify({ role: user.value.Role }))
        sessionStorage.setItem('userAbilities', JSON.stringify([{
          action: 'manage',
          subject: 'all',
        }]))
      } else {
        localStorage.setItem('accessToken', JSON.stringify(token.value))
        localStorage.setItem('userData', JSON.stringify(user.value))
        localStorage.setItem('userRole', JSON.stringify({ role: user.value.Role }))
        localStorage.setItem('userAbilities', JSON.stringify([{
          action: 'read',
          subject: 'ACLDemo',
        }]))

        sessionStorage.setItem('accessToken', JSON.stringify(token.value))
        sessionStorage.setItem('userData', JSON.stringify(user.value))
        sessionStorage.setItem('userRole', JSON.stringify({ role: user.value.Role }))
        sessionStorage.setItem('userAbilities', JSON.stringify([{
          action: 'all',
          subject: 'ACLDemo',
        }]))

        userAbilityRules.value = [{
          action: 'all',
          subject: 'ACLDemo',
        }]
      }

      toast('Successfully logged in!', {
        autoClose: 5000,
        theme: 'light',
        type: 'success',
      })

      localStorage.setItem('initialLogin', true)

      setTimeout(() => {
        router.push('/dashboard')
      }, 2000)

    } catch (error) {
      console.error('Login failed:', error)

      toast(error.response?.data?.message || 'Login failed', {
        autoClose: 5000,
        theme: 'light',
        type: 'error',
      })
    }
  }

  const register = async data => {
    try {
      const res = await axiosIns.post('register', data)
          
      toast('Successfully logged in!', {
        autoClose: 5000,
        theme: 'light',
        type: 'success',
      })

      setTimeout(() => {
        router.go('/')
      }, 2000)
    } catch (error) {
      console.error('Login failed:', error)

      toast(error.response?.data?.message || 'Login failed', {
        autoClose: 5000,
        theme: 'light',
        type: 'error',
      })
    }
  }

  const logout = async () => {
    $reset()
    localStorage.clear()
    sessionStorage.clear()
    userAbilityRules.value = []

    toast('Successfully logged out!', {
      autoClose: 5000,
      theme: 'light',
      type: 'success',
    })


    setTimeout(() => {
      router.go('/login')
    }, 2000)
  }

  return {
    token,
    user,
    $reset,
    login,
    register,
    logout,
  }
}, {
  persistedState: {
    persist: true,
  },
})
