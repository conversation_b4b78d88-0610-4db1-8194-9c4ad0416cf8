import { defineStore } from 'pinia'
import axiosIns from '@/composables/axios'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'

export const useProfileStore = defineStore('Profile', () => {
  const profile = ref(null)

  function $reset() {
    profile.value = null
  }

  const getProfile = async id => {
    console.log({ id })
    try {
      const res = await axiosIns.get(`api/users/${id}`)

      console.log({ res })

      const apiProfile =  res.data

      profile.value = apiProfile
    } catch (error) {
      toast(error.response?.data?.message || 'Profile Could Not Be Loaded', {
        autoClose: 5000,
        theme: 'light',
        type: 'error',
      })
    }
  }

  const updateProfile = async data => {
    console.log({ data })
    try {
      const res = await axiosIns.put(`api/users/${data.id}`, data)

      console.log({ res })

      const apiProfile =  res.data

      profile.value = apiProfile

      toast('Profile Updated Successfully!', {
        autoClose: 5000,
        theme: 'light',
        type: 'success',
      })
    } catch (error) {
      toast(error.response?.data?.message || 'Profile Could Not Be Updated', {
        autoClose: 5000,
        theme: 'light',
        type: 'error',
      })
    }
  }

  return {
    profile,
    $reset,
    getProfile,
    updateProfile,
  }
}, {
  persistedState: {
    persist: true,
  },
})
