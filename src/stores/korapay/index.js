import { defineStore } from 'pinia'
import axiosIns from '@/composables/axios'

export const useKorapayStore = defineStore('Korapay', () => {
  const respData = ref('')
  const isLoading = ref(false)
  const error = ref(null)

  function $reset() {
    respData.value = ''
    isLoading.value = false
    error.value = null
  }

  const chargeApi = async data => {
    isLoading.value = true
    error.value = null

    try {
      console.log('Sending Korapay payload:', data)

      const res = await axiosIns.post('pay/korapay/mobilemoney', data)

      console.log('Korapay charge API full response:', res)
      console.log('Korapay charge API response data:', res.data)

      // Korapay typically returns the payment link in different possible locations
      const paymentLink = res.data?.checkout_url ||
                         res.data?.data?.checkout_url ||
                         res.data?.link ||
                         res.data?.data?.link ||
                         res.data?.authorization_url ||
                         res.data?.data?.authorization_url

      respData.value = paymentLink

      // Return the full response for the component to handle
      return res.data
    } catch (err) {
      error.value = err.response?.data?.message || err.message || 'Payment initiation failed'
      console.error('Korapay payment failed:', err)
      console.error('Error response:', err.response?.data)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const initiatePayment = async (paymentData) => {
    isLoading.value = true
    error.value = null

    try {
      // Use Korapay-specific endpoint for payment initiation
      const res = await axiosIns.post('api/korapay/payments/initiate', paymentData)

      console.log('Korapay payment initiated:', res.data)

      // Korapay typically returns the payment link in different possible locations
      respData.value = res.data?.checkout_url || res.data?.data?.checkout_url || res.data?.link || res.data?.data?.link

      return res.data
    } catch (err) {
      error.value = err.response?.data?.message || 'Korapay payment initiation failed'
      console.error('Korapay payment initiation failed:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const verifyPayment = async (txRef) => {
    try {
      // Use Korapay-specific endpoint for payment verification
      const res = await axiosIns.get(`api/korapay/payments/verify?tx_ref=${txRef}`)

      console.log('Korapay payment verification:', res.data)

      return res.data
    } catch (err) {
      error.value = err.response?.data?.message || 'Payment verification failed'
      console.error('Korapay payment verification failed:', err)
      throw err
    }
  }

  return {
    respData,
    isLoading,
    error,
    $reset,
    chargeApi,
    initiatePayment,
    verifyPayment,
  }
})
