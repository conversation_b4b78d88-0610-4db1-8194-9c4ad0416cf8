import { defineStore } from 'pinia'
import axiosIns from '@/composables/axios'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'

export const useSwaggerStore = defineStore('Swagger', () => {
  const docs = ref(null)

  function $reset() {
    docs.value = null
  }

  const getDocs = async data => {
    try {
      const res = await axiosIns.get('swagger/doc.json', data)

      console.log({ res })

      const apiDocs =  res.data

      docs.value = apiDocs
    } catch (error) {
      toast(error.response?.data?.message || 'API Docs Could Not Be Loaded', {
        autoClose: 5000,
        theme: 'light',
        type: 'error',
      })
    }
  }

  return {
    docs,
    $reset,
    getDocs,
  }
}, {
  persistedState: {
    persist: true,
  },
})
