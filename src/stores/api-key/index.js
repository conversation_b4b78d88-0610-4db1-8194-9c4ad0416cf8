import { defineStore } from 'pinia'
import axiosIns from '@/composables/axios'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'

export const useApiKeyStore = defineStore('ApiKey', () => {
  const apiKey = ref(null)

  function $reset() {
    apiKey.value = null
  }

  const getApiKey = async data => {
    try {
      const res = await axiosIns.get('api/api-key', data)

      console.log({ res })

      const apiApiKey =  res.data

      apiKey.value = apiApiKey
    } catch (error) {
      toast(error.response?.data?.message || 'API Key Could Not Be Loaded', {
        autoClose: 5000,
        theme: 'light',
        type: 'error',
      })
    }
  }

  return {
    apiKey,
    $reset,
    getApiKey,
  }
}, {
  persistedState: {
    persist: true,
  },
})
