<script setup>
import moment from 'moment'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
  transaction: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['update:isDrawerOpen'])

// Use computed to create a two-way binding
const drawerOpen = computed({
  get: () => props.isDrawerOpen,
  set: value => emit('update:isDrawerOpen', value),
})
</script>

<template>
  <VNavigationDrawer
    v-model="drawerOpen"
    location="right"
    width="600"
    temporary
  >
    <VListItem title="Transaction Details" />

    <VDivider />

    <div v-if="transaction">
      <VRow class="px-5 py-5">
        <VCol cols="4">
          ID
        </VCol>
        <VCol cols="4">
          :
        </VCol>
        <VCol cols="4">
          {{ transaction.id }}
        </VCol>
        <VCol cols="4">
          Status
        </VCol>
        <VCol cols="4">
          :
        </VCol>
        <VCol cols="4">
          <VChip
            v-if="transaction.transactionStatus === 'successful'"
            color="success"
            variant="elevated"
          >
            {{ transaction.transactionStatus }}
          </VChip>
          <VChip
            v-if="transaction.transactionStatus === 'pending'"
            color="warning"
            variant="elevated"
          >
            {{ transaction.transactionStatus }}
          </VChip>
          <VChip
            v-if="transaction.transactionStatus === 'failed'"
            color="error"
            variant="elevated"
          >
            {{ transaction.transactionStatus }}
          </VChip>
        </VCol>
        <VCol cols="4">
          Currency
        </VCol>
        <VCol cols="4">
          :
        </VCol>
        <VCol cols="4">
          {{ transaction.currency }}
        </VCol>
        <VCol cols="4">
          Amount
        </VCol>
        <VCol cols="4">
          :
        </VCol>
        <VCol cols="4">
          {{ transaction.amount }}
        </VCol>
        <VCol cols="4">
          Type
        </VCol>
        <VCol cols="4">
          :
        </VCol>
        <VCol cols="4">
          {{ transaction.sourceOfFunds }}
        </VCol>
        <VCol cols="4">
          Processed
        </VCol>
        <VCol cols="4">
          :
        </VCol>
        <VCol cols="4">
          <VChip
            v-if="transaction.transactionStatus === 'successful'"
            color="success"
            variant="elevated"
          >
            <VIcon icon="ri-check-line" />
          </VChip>
          <VChip
            v-if="transaction.transactionStatus === 'pending'"
            color="warning"
            variant="elevated"
          >
            <VIcon icon="ri-error-warning-line" />
          </VChip>
          <VChip
            v-if="transaction.transactionStatus === 'failed'"
            color="error"
            variant="elevated"
          >
            <VIcon icon="ri-close-line" />
          </VChip>
        </VCol>
        <VCol cols="4">
          Date
        </VCol>
        <VCol cols="4">
          :
        </VCol>
        <VCol cols="4">
          {{ moment(transaction.createdAt).format('YYYY-MM-DD HH:mm:ss') }}
        </VCol>
      </VRow>
    </div>
  </VNavigationDrawer>
</template>

<style scoped lang="scss">
/**/
</style>
