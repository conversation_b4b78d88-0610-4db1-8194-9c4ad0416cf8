// 👉 Redirects
export const redirects = [
  // ℹ️ We are redirecting to different pages based on role.
  // NOTE: Role is just for UI purposes. ACL is based on abilities.
  {
    path: '/',
    name: 'index',
    redirect: to => {
      // TODO: Get type from backend
      const userData = JSON.parse(localStorage.getItem('userRole')) || {}

      if (userData?.role === 'admin')
        return { name: 'dashboard' }
      if (userData?.role === 'merchant' || userData?.role === 'reseller')
        return { name: 'dashboard' }

      return { name: 'login', query: to.query }
    },
  },
]
export const routes = []
