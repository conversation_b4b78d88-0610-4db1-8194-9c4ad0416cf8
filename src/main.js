import { createApp } from 'vue'
import App from '@/App.vue'
import { registerPlugins } from '@core/utils/plugins'
import { VCodeBlock } from '@wdns/vue-code-block'
import { initKorapay } from '@gray-adeyi/korapay-vue'

// Styles
import '@core/scss/template/index.scss'
import '@styles/styles.scss'

const publicKey = import.meta.env.VITE_KORAPAY_PUBLIC_KEY

// Create vue app
const app = createApp(App)

app.component('VCodeBlock', VCodeBlock)

// Register plugins
registerPlugins(app)

const korapay = await initKorapay({
  optional: publicKey,
})

app.use(korapay)

// Mount vue app
app.mount('#app')
