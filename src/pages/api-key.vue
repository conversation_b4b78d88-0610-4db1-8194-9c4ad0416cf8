<script setup>
import { useApiKeyStore } from '@/stores/api-key'
import { useAuthStore } from '@/stores/auth'

const store = useAuthStore()
const apiKeyStore = useApiKeyStore()

const { user } = storeToRefs(store)
const { apiKey } = storeToRefs(apiKeyStore)

onMounted(() => {
  if (!apiKey.value) {
    apiKeyStore.getApiKey({ id: user.value.id })
  }
})

const apikey = ref('')
</script>

<template>
  <VCard title="API Key">
    <VCardText>
      <VRow>
        <VCol cols="6">
          <!-- 👉 Password -->
          <VTextarea
            v-model="apikey"
            label="********************************"
            disabled
          />
        </VCol>
        <VCol>
          <div class="demo-space-x">
            <VBtn variant="text">
              <VIcon
                icon="ri-file-copy-line"
                size="20"
              />
              <VTooltip
                activator="parent"
                location="top"
              >
                Copy API Key
              </VTooltip>
            </VBtn>
          </div>
        </VCol>
      </VRow>
    </VCardText>
  </VCard>
</template>

<style scoped lang="scss">
/**/
</style>
