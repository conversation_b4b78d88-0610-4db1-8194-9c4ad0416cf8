<script setup>
import { useSwaggerStore } from '@/stores/swagger'
import html2pdf from 'html2pdf.js'

const swaggerStore = useSwaggerStore()

const { docs } = storeToRefs(swaggerStore)

onMounted(() => {
  if(!docs.value) {
    swaggerStore.getDocs()
  }
})

const currentTab = ref(0)

const firstDefinition = computed(() => {
  const defs = docs.value.definitions
  if (defs && typeof defs === 'object') {
    const entries = Object.entries(defs)
    if (entries.length) {
      return {
        name: entries[0][0],
        schema: entries[0][1],
      }
    }
  }

  return null
})

const pdfContent = ref(null)

const downloadPdf = () => {
  const element = pdfContent.value

  const opt = {
    margin: 0.5,
    filename: 'api-docs.pdf',
    image: { type: 'jpeg', quality: 0.98 },
    html2canvas: { scale: 2 },
    jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' },
  }

  html2pdf().set(opt).from(element).save()
}
</script>

<template>
  <VCard title="API Docs">
    <VCardText>
      <VRow>
        <VCol class="text-right">
          <VBtn @click="downloadPdf">
            <VIcon icon="ri-download-2-line" />
            Export to PDF
          </VBtn>
        </VCol>
      </VRow>
    </VCardText>

    <VCardText title="API Docs">
      <div ref="pdfContent">
        <div v-if="docs?.paths">
          <VTabs
            v-model="currentTab"
            class="v-tabs-pill"
          >
            <VTab
              v-for="(value, endpoint, index) in docs.paths"
              :key="endpoint"
              :value="index"
            >
              {{ endpoint }}
            </VTab>
          </VTabs>

          <VWindow
            v-model="currentTab"
            class="mt-5"
          >
            <VWindowItem
              v-for="(value, endpoint, index) in docs.paths"
              :key="endpoint"
              :value="index"
            >
              <h3 class="text-primary">
                {{ endpoint }}
              </h3>
              <br>

              <h3 class="pb-5 text-primary">
                Method:
              </h3>
              <VCodeBlock
                :code="JSON.stringify(value, null, 2)"
                highlightjs
                lang="json"
                theme="tokyo-night-dark"
              />

              <h3 class="pt-5 pb-5 text-primary">
                Definitions / Payload:
              </h3>

              <VCodeBlock
                :code="JSON.stringify(firstDefinition.schema, null, 2)"
                highlightjs
                lang="json"
                theme="tokyo-night-dark"
              />
            </VWindowItem>
          </VWindow>
        </div>
        <div v-else>
          <VProgressCircular
            indeterminate
            color="primary"
          />
        </div>
      </div>
    </VCardText>
  </VCard>
</template>

<style scoped lang="scss">
@media print {
  .no-print {
    display: none !important;
  }
}
</style>
