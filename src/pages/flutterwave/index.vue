<script setup>
import { ref, computed, watch } from 'vue'
import axiosIns from '@/composables/axios'

const router = useRouter()

// Form data
const phone = ref('**********20')
const amount = ref('1000')
const fullname = ref('<PERSON>')
const email = ref('<EMAIL>')
const txRef = ref('MC-3243f')
const paymentType = ref('mobile_money_uganda')
const country = ref('UG') // You could also change this dynamically based on type
const network = ref('MTN') // You can expose dropdowns per region
const order_ref = ref('test-order-ref-123')

// UI state
const isLoading = ref(false)
const formErrors = ref({})

// Generate transaction reference automatically
const generateTxRef = () => {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  
  return `TX-${timestamp}-${random}`.toUpperCase()
}

// Payment type configurations
const paymentConfigs = {
  'mpesa': { country: 'KE', networks: ['Safaricom'], currency: 'KES' },
  'mobile_money_uganda': { country: 'UG', networks: ['MTN', 'Airtel'], currency: 'UGX' },
  'mobile_money_rwanda': { country: 'RW', networks: ['MTN', 'Airtel'], currency: 'RWF' },
  'mobile_money_ghana': { country: 'GH', networks: ['MTN', 'Vodafone', 'AirtelTigo'], currency: 'GHS' },
  'mobile_money_franco': { country: 'CI', networks: ['MTN', 'Orange'], currency: 'XOF' },
  'mobile_money_tanzania': { country: 'TZ', networks: ['Vodacom', 'Airtel', 'Tigo'], currency: 'TZS' },
  'mobile_money_zambia': { country: 'ZM', networks: ['MTN', 'Airtel'], currency: 'ZMW' },
  'bank_transfer_nigeria': { country: 'NG', networks: ['Bank'], currency: 'NGN' },
  'bank_transfer_ghana': { country: 'GH', networks: ['Bank'], currency: 'GHS' },
  'bank_transfer_kenya': { country: 'KE', networks: ['Bank'], currency: 'KES' },
  'bank_transfer_egypt': { country: 'EG', networks: ['Bank'], currency: 'EGP' },
  'opay': { country: 'NG', networks: ['Voucher'], currency: 'NGN' },
  'voucher_payment': { country: 'NG', networks: ['Voucher'], currency: 'NGN' },
  'applepay': { country: 'NG', networks: ['Voucher'], currency: 'NGN' },
  'googlepay': { country: 'NG', networks: ['Voucher'], currency: 'NGN' },
}

// Available networks based on payment type
const availableNetworks = computed(() => {
  return paymentConfigs[paymentType.value]?.networks || []
})

// Watch payment type changes to update country and reset network
watch(paymentType, newType => {
  const config = paymentConfigs[newType]
  if (config) {
    country.value = config.country
    network.value = config.networks[0] || ''
  }
})

// Form validation
const validateForm = () => {
  const errors = {}

  if (!fullname.value.trim()) errors.fullname = 'Full name is required'
  if (!email.value.trim()) errors.email = 'Email is required'
  else if (!/\S+@\S+\.\S+/.test(email.value)) errors.email = 'Email is invalid'
  if (!phone.value.trim()) errors.phone = 'Phone number is required'
  if (!amount.value || amount.value <= 0) errors.amount = 'Amount must be greater than 0'
  if (!network.value) errors.network = 'Network is required'

  formErrors.value = errors
  
  return Object.keys(errors).length === 0
}

const pay = async () => {
  if (!validateForm()) return

  isLoading.value = true

  try {
    const config = paymentConfigs[paymentType.value]

    const res = await axiosIns.post('api/payments/initiate', {
      tx_ref: txRef.value,
      payment_type: paymentType.value,
      phone_number: phone.value,
      amount: amount.value,
      currency: config?.currency || 'UGX',
      full_name: fullname.value,
      email: email.value,
      network: network.value,
      order_ref: order_ref.value,
      country: country.value,
      redirect_url: 'http://localhost:5173/flutterwave/redirect-results',
      narration: 'Payment via Flutterwave Portal',
    })

    const paymentLink = res.data?.data?.link

    if (paymentLink) {
      router.push({
        name: 'flutterwave-payment-redirect',
        query: { link: encodeURIComponent(paymentLink) },
      })
    } else {
      throw new Error('Payment link not received')
    }
  } catch (e) {
    console.error('Payment initiation failed:', e)

    // You could add a toast notification here instead of alert
    alert('Payment failed. Please try again.')
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <div class="payment-page">
    <!-- Header Section -->
    <div class="text-center mb-8">
      <VIcon
        icon="ri-secure-payment-line"
        size="48"
        color="primary"
        class="mb-4"
      />
      <h1 class="text-h4 font-weight-bold mb-2">
        Flutterwave Payment Gateway
      </h1>
      <p class="text-body-1 text-medium-emphasis">
        Secure payment processing with Flutterwave
      </p>
    </div>

    <!-- Payment Form -->
    <VCard
      class="mx-auto payment-card"
      max-width="800"
      elevation="8"
    >
      <VCardTitle class="d-flex align-center pa-6 bg-primary">
        <VIcon
          icon="ri-wallet-3-line"
          class="me-3"
          color="white"
        />
        <span class="text-white">Flutterwave Payment Details</span>
      </VCardTitle>

      <VCardText class="pa-6">
        <form @submit.prevent="pay">
          <!-- Personal Information Section -->
          <div class="mb-6">
            <h3 class="text-h6 mb-4 d-flex align-center">
              <VIcon
                icon="ri-user-line"
                class="me-2"
                color="primary"
              />
              Personal Information
            </h3>
            <VRow>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="fullname"
                  label="Full Name"
                  prepend-inner-icon="ri-user-line"
                  variant="outlined"
                  :error-messages="formErrors.fullname"
                  required
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="email"
                  label="Email Address"
                  prepend-inner-icon="ri-mail-line"
                  variant="outlined"
                  type="email"
                  :error-messages="formErrors.email"
                  required
                />
              </VCol>
            </VRow>
          </div>

          <!-- Payment Information Section -->
          <div class="mb-6">
            <h3 class="text-h6 mb-4 d-flex align-center">
              <VIcon
                icon="ri-smartphone-line"
                class="me-2"
                color="primary"
              />
              Payment Information
            </h3>
            <VRow>
              <VCol
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="paymentType"
                  label="Payment Method"
                  prepend-inner-icon="ri-bank-card-line"
                  variant="outlined"
                  :items="[
                    { value: 'mpesa', title: 'M-PESA (Kenya)' },
                    { value: 'mobile_money_uganda', title: 'Mobile Money (Uganda)' },
                    { value: 'mobile_money_rwanda', title: 'Mobile Money (Rwanda)' },
                    { value: 'mobile_money_ghana', title: 'Mobile Money (Ghana)' },
                    { value: 'mobile_money_franco', title: 'Mobile Money (Francophone)' },
                    { value: 'mobile_money_tanzania', title: 'Mobile Money (Tanzania)' },
                    { value: 'mobile_money_zambia', title: 'Mobile Money (Zambia)' },
                    { value: 'bank_transfer_nigeria', title: 'Bank Transfer (Nigeria)' },
                    { value: 'bank_transfer_ghana', title: 'Bank Transfer (Ghana)' },
                    { value: 'bank_transfer_kenya', title: 'Bank Transfer (Kenya)' },
                    { value: 'bank_transfer_egypt', title: 'Bank Transfer (Egypt)' },
                    { value: 'opay', title: 'OPay' },
                    { value: 'voucher_payment', title: 'Voucher Payment' },
                    { value: 'applepay', title: 'Apple Pay' },
                    { value: 'googlepay', title: 'Google Pay' },
                  ]"
                  required
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="network"
                  label="Network Provider"
                  prepend-inner-icon="ri-signal-tower-line"
                  variant="outlined"
                  :items="availableNetworks"
                  :error-messages="formErrors.network"
                  required
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="phone"
                  label="Phone Number"
                  prepend-inner-icon="ri-phone-line"
                  variant="outlined"
                  :error-messages="formErrors.phone"
                  placeholder="e.g. **********"
                  required
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="amount"
                  label="Amount"
                  prepend-inner-icon="ri-money-dollar-circle-line"
                  variant="outlined"
                  type="number"
                  min="1"
                  :error-messages="formErrors.amount"
                  :suffix="paymentConfigs[paymentType]?.currency || 'UGX'"
                  required
                />
              </VCol>
            </VRow>
          </div>

          <!-- Transaction Details Section -->
          <div class="mb-6">
            <h3 class="text-h6 mb-4 d-flex align-center">
              <VIcon
                icon="ri-file-list-3-line"
                class="me-2"
                color="primary"
              />
              Transaction Details
            </h3>
            <VRow>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="country"
                  label="Country Code"
                  prepend-inner-icon="ri-global-line"
                  variant="outlined"
                  readonly
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="txRef"
                  label="Transaction Reference"
                  prepend-inner-icon="ri-hashtag"
                  variant="outlined"
                  hint="Auto-generated unique reference"
                />
              </VCol>
              <VCol cols="12">
                <VTextField
                  v-model="order_ref"
                  label="Order Reference"
                  prepend-inner-icon="ri-shopping-cart-line"
                  variant="outlined"
                  hint="Auto-generated order reference"
                />
              </VCol>
            </VRow>
          </div>

          <!-- Submit Button -->
          <div class="text-center">
            <VBtn
              type="submit"
              color="primary"
              size="large"
              :loading="isLoading"
              :disabled="isLoading"
              class="px-8 py-3"
              elevation="2"
            >
              <VIcon
                icon="ri-secure-payment-line"
                class="me-2"
              />
              {{ isLoading ? 'Processing...' : 'Pay with Flutterwave' }}
            </VBtn>
          </div>
        </form>
      </VCardText>
    </VCard>

    <!-- Security Notice -->
    <VCard
      class="mx-auto mt-6 security-notice"
      max-width="800"
      variant="tonal"
      color="success"
    >
      <VCardText class="text-center pa-4">
        <VIcon
          icon="ri-shield-check-line"
          class="me-2"
          color="success"
        />
        <span class="text-success">
          Your payment is secured with Flutterwave's 256-bit SSL encryption
        </span>
      </VCardText>
    </VCard>
  </div>
</template>

<style scoped>
.payment-page {
  padding: 2rem 1rem;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.payment-card {
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.payment-card .v-card-title {
  border-radius: 16px 16px 0 0 !important;
  background: linear-gradient(135deg, rgb(var(--v-theme-primary)) 0%, rgba(var(--v-theme-primary), 0.8) 100%) !important;
}

.security-notice {
  border-radius: 12px !important;
  background: rgba(var(--v-theme-on-secondary), 1) !important;
  border: 1px solid rgba(var(--v-theme-success), 0.2) !important;
}

.v-text-field--variant-outlined .v-field {
  border-radius: 12px;
}

.v-select--variant-outlined .v-field {
  border-radius: 12px;
}

.v-btn {
  border-radius: 12px !important;
  text-transform: none !important;
  font-weight: 600 !important;
}

@media (max-width: 768px) {
  .payment-page {
    padding: 1rem 0.5rem;
  }

  .payment-card {
    margin: 0 !important;
  }
}
</style>
