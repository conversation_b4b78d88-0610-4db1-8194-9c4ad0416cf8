<script setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const countdown = ref(5)
const hasError = ref(false)

onMounted(() => {
  const paymentLink = route.query.link
  if (paymentLink) {
    // Start countdown
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)

        // Redirect to Flutterwave
        window.location.href = decodeURIComponent(paymentLink)
      }
    }, 1000)
  } else {
    hasError.value = true
  }
})

const goBack = () => {
  router.push('/flutterwave')
}
</script>

<template>
  <div class="payment-redirect-page">
    <!-- Error State -->
    <VCard
      v-if="hasError"
      class="mx-auto redirect-card"
      max-width="500"
      elevation="8"
    >
      <VCardText class="text-center pa-8">
        <VIcon
          icon="ri-error-warning-line"
          size="64"
          color="error"
          class="mb-4"
        />
        <h2 class="text-h5 font-weight-bold mb-4 text-error">
          Invalid Payment Link
        </h2>
        <p class="text-body-1 mb-6">
          The Flutterwave payment link is invalid or has expired.
        </p>
        <VBtn
          color="primary"
          @click="goBack"
        >
          <VIcon
            icon="ri-arrow-left-line"
            class="me-2"
          />
          Go Back
        </VBtn>
      </VCardText>
    </VCard>

    <!-- Redirect State -->
    <VCard
      v-else
      class="mx-auto redirect-card"
      max-width="500"
      elevation="8"
    >
      <VCardText class="text-center pa-8">
        <VProgressCircular
          :model-value="((5 - countdown) / 5) * 100"
          size="80"
          width="6"
          color="primary"
          class="mb-4"
        >
          <span class="text-h6 font-weight-bold">{{ countdown }}</span>
        </VProgressCircular>

        <h2 class="text-h5 font-weight-bold mb-4">
          Redirecting to Flutterwave
        </h2>
        <p class="text-body-1 mb-4">
          You will be redirected to Flutterwave in {{ countdown }} seconds...
        </p>

        <VAlert
          type="info"
          variant="tonal"
          class="mb-4"
        >
          <VIcon
            icon="ri-information-line"
            class="me-2"
          />
          Please do not close this window during the payment process
        </VAlert>

        <VBtn
          color="primary"
          variant="outlined"
          @click="goBack"
        >
          <VIcon
            icon="ri-arrow-left-line"
            class="me-2"
          />
          Cancel
        </VBtn>
      </VCardText>
    </VCard>
  </div>
</template>

<style scoped>
.payment-redirect-page {
  padding: 2rem 1rem;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.redirect-card {
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.v-btn {
  border-radius: 12px !important;
  text-transform: none !important;
  font-weight: 600 !important;
}

@media (max-width: 768px) {
  .payment-redirect-page {
    padding: 1rem 0.5rem;
  }

  .redirect-card {
    margin: 0 !important;
  }
}
</style>
