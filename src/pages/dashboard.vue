<script setup>
import TotalTransactionsChart from "@/views/pages/dashboard/TotalTransactionsChart.vue"
import WeeklyTransactionsChart from "@/views/pages/dashboard/WeeklyTransactionsChart.vue"

const router = useRouter()

onMounted(() => {
  const isInitialLogin = localStorage.getItem('initialLogin')

  if (isInitialLogin) {
    localStorage.removeItem('initialLogin')
    router.go()
  }
})
</script>

<template>
  <VRow>
    <VCol cols="8">
      <TotalTransactionsChart />
    </VCol>
    <VCol>
      <WeeklyTransactionsChart />
    </VCol>
  </VRow>
</template>

<style scoped lang="scss">
/**/
</style>
