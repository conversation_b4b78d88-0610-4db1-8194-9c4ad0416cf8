<script setup>
import { ref, computed } from 'vue'
import { Currency } from '@gray-adeyi/korapay-vue'

const amount = ref(5000) // smallest currency unit, e.g. kobo

const customer = ref({
  name: '<PERSON>',
  email: '<EMAIL>',
})

const payload = computed(() => ({
  reference: crypto.randomUUID(),
  amount: amount.value,
  currency: Currency.NGN,
  customer: customer.value,
}))
</script>

<template>
  <div>
    <input
      v-model="customer.name"
      placeholder="Name"
    >
    <input
      v-model="customer.email"
      placeholder="Email"
    >
    <input
      v-model.number="amount"
      type="number"
      placeholder="Amount"
    >
    <KorapayButton
      :payload="payload"
      text="Pay Now"
    />
  </div>
</template>


<style scoped>
.secured__banner {
  display: none;
}
</style>
