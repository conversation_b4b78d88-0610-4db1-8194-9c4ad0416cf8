<script setup>
import { useTransactionStore } from '@/stores/transactions'
import moment from 'moment'
import TransactionDrawer from "@/views/pages/transactions/TransactionDrawer.vue"

const transactionStore = useTransactionStore()

const { transactions, transaction } = storeToRefs(transactionStore)

const search = ref('')
const isDrawerOpen = ref(false)

onMounted(() => {
  if (!transactions.value) {
    transactionStore.getTransactions()
  }
})

// headers
const headers = [
  {
    title: 'ID',
    key: 'id',
  },
  {
    title: 'Date',
    key: 'createdAt',
  },
  {
    title: 'Transaction Report',
    key: 'transactionReport',
  },
  {
    title: 'Status',
    key: 'transactionStatus',
    sortable: false,
  },
  {
    title: 'Amount',
    key: 'amount',
  },
  {
    title: 'Currency',
    key: 'currency',
  },
  {
    title: 'Reference',
    key: 'txRef',
  },
  {
    title: 'View',
    key: 'view',
    sortable: false,
  },
]

const resolveStatusColor = status => {
  if (status === 'pending')
    return 'warning'
  if (status === 'successful')
    return 'success'
  if (status === 'failed')
    return 'error'
}

const viewItem = id => {
  console.log('view item', { id })

  transactionStore.getTransaction(id)

  isDrawerOpen.value = true
}
</script>

<template>
  <VCard title="Transactions">
    <VCardText>
      <VRow>
        <VCol
          cols="12"
          offset-md="8"
          md="4"
        >
          <VTextField
            v-model="search"
            label="Search"
            placeholder="Search ..."
            append-inner-icon="ri-search-line"
            single-line
            hide-details
            dense
            outlined
          />
        </VCol>
      </VRow>
    </VCardText>

    <!-- 👉 Data Table  -->
    <VDataTable
      :headers="headers"
      :items="transactions || []"
      :search="search"
      :items-per-page="5"
      class="text-no-wrap"
    >
      <!-- Status -->
      <template #item.createdAt="{ item }">
        {{ moment(item.createdAt).format('YYYY-MM-DD HH:mm:ss') }}
      </template>

      <!-- Status -->
      <template #item.transactionStatus="{ item }">
        <VChip
          :color="resolveStatusColor(item.transactionStatus)"
          :class="`text-${resolveStatusColor(item.transactionStatus)}`"
          size="small"
          class="font-weight-medium"
        >
          {{ item.transactionStatus }}
        </VChip>
      </template>

      <!-- Delete -->
      <template #item.view="{ item }">
        <IconBtn
          size="small"
          @click="viewItem(item.id)"
        >
          <VIcon icon="ri-eye-line" />
        </IconBtn>
      </template>
    </VDataTable>
  </VCard>

  <TransactionDrawer
    v-model:is-drawer-open="isDrawerOpen"
    :transaction="transaction"
  />
</template>

<style scoped lang="scss">
/**/
</style>
