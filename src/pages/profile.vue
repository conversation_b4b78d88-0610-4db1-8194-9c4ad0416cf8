<script setup>
import { useAuthStore } from '@/stores/auth'
import { useProfileStore } from '@/stores/user/profile'

const store = useAuthStore()
const profileStore = useProfileStore()

const { user } = storeToRefs(store)
const { profile } = storeToRefs(profileStore)

onMounted(() => {
  if (!profile.value) {
    profileStore.getProfile(user.value.ID)
  }
})

const updateProfile = () => {
  profileStore.updateProfile({
    id: user.value.ID,
    name: user.value.Name,
    email: user.value.Email,
    phone: user.value.Phone,
    location: user.value.Location,
    role: user.value.Role,
  })
}
</script>

<template>
  <VCard title="Profile">
    <VCardText>
      <VForm @submit.prevent="updateProfile">
        <VRow>
          <!-- 👉 Name -->
          <VCol
            cols="12"
            md="6"
          >
            <VTextField
              v-model="user.Name"
              label="Name"
              placeholder="<PERSON>"
            />
          </VCol>

          <!-- 👉 Email -->
          <VCol
            cols="12"
            md="6"
          >
            <VTextField
              v-model="user.Email"
              label="Email"
              placeholder="<EMAIL>"
            />
          </VCol>

          <!-- 👉 Phone Number -->
          <VCol
            cols="12"
            md="6"
          >
            <VTextField
              v-model="user.Phone"
              label="Phone Number"
              placeholder="****** 456 7890"
            />
          </VCol>

          <!-- 👉 Location -->
          <VCol
            cols="12"
            md="6"
          >
            <VTextField
              v-model="user.Location"
              label="Location"
              placeholder="New York"
            />
          </VCol>

          <VCol
            cols="12"
            class="d-flex gap-4"
          >
            <VBtn type="submit">
              Update
            </VBtn>

            <VBtn
              type="reset"
              color="secondary"
              variant="tonal"
            >
              Reset
            </VBtn>
          </VCol>
        </VRow>
      </VForm>
    </VCardText>
  </VCard>
</template>
