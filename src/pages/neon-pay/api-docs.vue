<script setup>
import { useSwaggerStore } from '@/stores/swagger'
import logo from '@images/logo/neon-pay-logo.png'

const swaggerStore = useSwaggerStore()

const { docs } = storeToRefs(swaggerStore)

onMounted(() => {
  if(!docs.value) {
    swaggerStore.getDocs()
  }
})

const currentTab = ref(0)

const firstDefinition = computed(() => {
  const defs = docs.value.definitions
  if (defs && typeof defs === 'object') {
    const entries = Object.entries(defs)
    if (entries.length) {
      return {
        name: entries[0][0],
        schema: entries[0][1],
      }
    }
  }
  
  return null
})
</script>

<template>
  <VCard title="API Docs">
    <VCardText class="mb-15">
      <VRow class="justify-center align-center">
        <VCol align="center">
          <VImg
            :src="logo"
            width="500"
          />
        </VCol>
      </VRow>
    </VCardText>
    <VCardText>
      <div v-if="docs?.paths">
        <VTabs
          v-model="currentTab"
          class="v-tabs-pill"
        >
          <VTab
            v-for="(value, endpoint, index) in docs.paths"
            :key="endpoint"
            :value="index"
          >
            {{ endpoint }}
          </VTab>
        </VTabs>

        <VWindow
          v-model="currentTab"
          class="mt-5"
        >
          <VWindowItem
            v-for="(value, endpoint, index) in docs.paths"
            :key="endpoint"
            :value="index"
          >
            <h3>{{ endpoint }}</h3>
            <br>

            <h3 class="pb-5">
              Method:
            </h3>
            <VCodeBlock
              :code="JSON.stringify(value, null, 2)"
              highlightjs
              lang="json"
              theme="tokyo-night-dark"
            />

            <h3 class="pt-5 pb-5">
              Definitions / Payload:
            </h3>

            <VCodeBlock
              :code="JSON.stringify(firstDefinition.schema, null, 2)"
              highlightjs
              lang="json"
              theme="tokyo-night-dark"
            />
          </VWindowItem>
        </VWindow>
      </div>
      <div v-else>
        <VProgressCircular
          indeterminate
          color="primary"
        />
      </div>
    </VCardText>
  </VCard>
</template>

<style scoped lang="scss">
/**/
</style>
