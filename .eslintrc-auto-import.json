{"globals": {"$api": true, "COOKIE_MAX_AGE_1_YEAR": true, "Component": true, "ComponentPublicInstance": true, "ComputedRef": true, "DirectiveBinding": true, "EffectScope": true, "ExtractDefaultPropTypes": true, "ExtractPropTypes": true, "ExtractPublicPropTypes": true, "InjectionKey": true, "MaybeRef": true, "MaybeRefOrGetter": true, "PropType": true, "Ref": true, "VNode": true, "WritableComputedRef": true, "acceptHMRUpdate": true, "alphaDashValidator": true, "alphaValidator": true, "asyncComputed": true, "autoResetRef": true, "avatarText": true, "betweenValidator": true, "computed": true, "computedAsync": true, "computedEager": true, "computedInject": true, "computedWithControl": true, "confirmedValidator": true, "controlledComputed": true, "controlledRef": true, "createApp": true, "createEventHook": true, "createGenericProjection": true, "createGlobalState": true, "createInjectionState": true, "createPinia": true, "createProjection": true, "createReactiveFn": true, "createReusableTemplate": true, "createSharedComposable": true, "createTemplatePromise": true, "createUnrefFn": true, "createUrl": true, "customRef": true, "debouncedRef": true, "debouncedWatch": true, "defineAsyncComponent": true, "defineComponent": true, "definePage": true, "defineStore": true, "eagerComputed": true, "effectScope": true, "emailValidator": true, "extendRef": true, "formatDate": true, "formatDateToMonthShort": true, "getActivePinia": true, "getCurrentInstance": true, "getCurrentScope": true, "h": true, "hexToRgb": true, "ignorableWatch": true, "inject": true, "injectLocal": true, "integerValidator": true, "isDefined": true, "isEmpty": true, "isEmptyArray": true, "isNullOrUndefined": true, "isObject": true, "isProxy": true, "isReactive": true, "isReadonly": true, "isRef": true, "isToday": true, "kFormatter": true, "lengthValidator": true, "logicAnd": true, "logicNot": true, "logicOr": true, "makeDestructurable": true, "mapActions": true, "mapGetters": true, "mapState": true, "mapStores": true, "mapWritableState": true, "markRaw": true, "nextTick": true, "onActivated": true, "onBeforeMount": true, "onBeforeRouteLeave": true, "onBeforeRouteUpdate": true, "onBeforeUnmount": true, "onBeforeUpdate": true, "onClickOutside": true, "onDeactivated": true, "onErrorCaptured": true, "onKeyStroke": true, "onLongPress": true, "onMounted": true, "onRenderTracked": true, "onRenderTriggered": true, "onScopeDispose": true, "onServerPrefetch": true, "onStartTyping": true, "onUnmounted": true, "onUpdated": true, "onWatcherCleanup": true, "paginationMeta": true, "passwordValidator": true, "pausableWatch": true, "prefixWithPlus": true, "provide": true, "provideLocal": true, "reactify": true, "reactifyObject": true, "reactive": true, "reactiveComputed": true, "reactiveOmit": true, "reactivePick": true, "readonly": true, "ref": true, "refAutoReset": true, "refDebounced": true, "refDefault": true, "refThrottled": true, "refWithControl": true, "regexValidator": true, "registerPlugins": true, "requiredValidator": true, "resolveComponent": true, "resolveRef": true, "resolveUnref": true, "resolveVuetifyTheme": true, "rgbaToHex": true, "setActivePinia": true, "setMapStoreSuffix": true, "shallowReactive": true, "shallowReadonly": true, "shallowRef": true, "storeToRefs": true, "syncRef": true, "syncRefs": true, "templateRef": true, "throttledRef": true, "throttledWatch": true, "toRaw": true, "toReactive": true, "toRef": true, "toRefs": true, "toValue": true, "triggerRef": true, "tryOnBeforeMount": true, "tryOnBeforeUnmount": true, "tryOnMounted": true, "tryOnScopeDispose": true, "tryOnUnmounted": true, "unref": true, "unrefElement": true, "until": true, "urlValidator": true, "useAbs": true, "useActiveElement": true, "useAnimate": true, "useApi": true, "useArrayDifference": true, "useArrayEvery": true, "useArrayFilter": true, "useArrayFind": true, "useArrayFindIndex": true, "useArrayFindLast": true, "useArrayIncludes": true, "useArrayJoin": true, "useArrayMap": true, "useArrayReduce": true, "useArraySome": true, "useArrayUnique": true, "useAsyncQueue": true, "useAsyncState": true, "useAttrs": true, "useAverage": true, "useBase64": true, "useBattery": true, "useBluetooth": true, "useBreakpoints": true, "useBroadcastChannel": true, "useBrowserLocation": true, "useCached": true, "useCeil": true, "useClamp": true, "useClipboard": true, "useClipboardItems": true, "useCloned": true, "useColorMode": true, "useConfirmDialog": true, "useCookie": true, "useCounter": true, "useCssModule": true, "useCssVar": true, "useCssVars": true, "useCurrentElement": true, "useCycleList": true, "useDark": true, "useDateFormat": true, "useDebounce": true, "useDebounceFn": true, "useDebouncedRefHistory": true, "useDeviceMotion": true, "useDeviceOrientation": true, "useDevicePixelRatio": true, "useDevicesList": true, "useDisplayMedia": true, "useDocumentVisibility": true, "useDraggable": true, "useDropZone": true, "useElementBounding": true, "useElementByPoint": true, "useElementHover": true, "useElementSize": true, "useElementVisibility": true, "useEventBus": true, "useEventListener": true, "useEventSource": true, "useEyeDropper": true, "useFavicon": true, "useFetch": true, "useFileDialog": true, "useFileSystemAccess": true, "useFloor": true, "useFocus": true, "useFocusWithin": true, "useFps": true, "useFullscreen": true, "useGamepad": true, "useGenerateImageVariant": true, "useGeolocation": true, "useI18n": true, "useId": true, "useIdle": true, "useImage": true, "useInfiniteScroll": true, "useIntersectionObserver": true, "useInterval": true, "useIntervalFn": true, "useKeyModifier": true, "useLastChanged": true, "useLocalStorage": true, "useMagicKeys": true, "useManualRefHistory": true, "useMath": true, "useMax": true, "useMediaControls": true, "useMediaQuery": true, "useMemoize": true, "useMemory": true, "useMin": true, "useModel": true, "useMounted": true, "useMouse": true, "useMouseInElement": true, "useMousePressed": true, "useMutationObserver": true, "useNavigatorLanguage": true, "useNetwork": true, "useNow": true, "useObjectUrl": true, "useOffsetPagination": true, "useOnline": true, "usePageLeave": true, "useParallax": true, "useParentElement": true, "usePerformanceObserver": true, "usePermission": true, "usePointer": true, "usePointerLock": true, "usePointerSwipe": true, "usePrecision": true, "usePreferredColorScheme": true, "usePreferredContrast": true, "usePreferredDark": true, "usePreferredLanguages": true, "usePreferredReducedMotion": true, "usePrevious": true, "useProjection": true, "useRafFn": true, "useRefHistory": true, "useResizeObserver": true, "useResponsiveLeftSidebar": true, "useRound": true, "useRoute": true, "useRouter": true, "useScreenOrientation": true, "useScreenSafeArea": true, "useScriptTag": true, "useScroll": true, "useScrollLock": true, "useSessionStorage": true, "useShare": true, "useSkins": true, "useSlots": true, "useSorted": true, "useSpeechRecognition": true, "useSpeechSynthesis": true, "useStepper": true, "useStorageAsync": true, "useStyleTag": true, "useSum": true, "useSupported": true, "useSwipe": true, "useTemplateRef": true, "useTemplateRefsList": true, "useTextDirection": true, "useTextSelection": true, "useTextareaAutosize": true, "useThrottle": true, "useThrottleFn": true, "useThrottledRefHistory": true, "useTimeAgo": true, "useTimeout": true, "useTimeoutFn": true, "useTimeoutPoll": true, "useTimestamp": true, "useTitle": true, "useToNumber": true, "useToString": true, "useToggle": true, "useTransition": true, "useTrunc": true, "useUrlSearchParams": true, "useUserMedia": true, "useVModel": true, "useVModels": true, "useVibrate": true, "useVirtualList": true, "useWakeLock": true, "useWebNotification": true, "useWebSocket": true, "useWebWorker": true, "useWebWorkerFn": true, "useWindowFocus": true, "useWindowScroll": true, "useWindowSize": true, "watch": true, "watchArray": true, "watchAtMost": true, "watchDebounced": true, "watchDeep": true, "watchEffect": true, "watchIgnorable": true, "watchImmediate": true, "watchOnce": true, "watchPausable": true, "watchPostEffect": true, "watchSyncEffect": true, "watchThrottled": true, "watchTriggerable": true, "watchWithFilter": true, "whenever": true, "axios": true, "useAbility": true}}